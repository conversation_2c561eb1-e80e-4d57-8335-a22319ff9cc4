package chunks_splitter

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/iqhater/video_auto_proccessing_pipeline/internal/plugins"
)

var _ plugins.TaskPlugin = (*ChunksSplitter)(nil)

type ChunksSplitter struct {
	Source    []byte
	ChunkSize int
}

func (cs *ChunksSplitter) Name() string {
	return "split_chunks"
}

func (cs *ChunksSplitter) Setup(cfg map[string]any) error {
	return nil
}

func (cs *ChunksSplitter) Process(ctx context.Context) ([]byte, error) {

	// Создаем временную директорию для хранения чанков
	tempDir, err := os.MkdirTemp("temp", "video_chunks_")
	if err != nil {
		log.Printf("Ошибка создания временной директории: %v", err)
		return nil, errors.New(err.Error())
	}

	// Сохраняем исходный файл во временную директорию
	tempSourcePath := filepath.Join(tempDir, "source_file")
	if err := os.WriteFile(tempSourcePath, cs.Source, 0644); err != nil {
		log.Printf("Ошибка сохранения исходного файла: %v", err)
		return nil, errors.New(err.Error())
	}

	// Определяем длительность видео для логирования
	probeCmd := exec.Command("./bin/ffprobe",
		"-v", "error",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		tempSourcePath)

	output, err := probeCmd.Output()
	if err != nil {
		log.Printf("Ошибка получения длительности видео: %v", err)
		return nil, errors.New(err.Error())
	}

	duration, err := strconv.ParseFloat(strings.TrimSpace(string(output)), 64)
	if err != nil {
		log.Printf("Ошибка парсинга длительности: %v", err)
		return nil, errors.New(err.Error())
	}

	log.Printf("Длительность видео: %.2f секунд", duration)

	// Рассчитываем количество чанков
	numChunks := int(math.Ceil(duration / float64(cs.ChunkSize)))
	log.Printf("Разбиваем на примерно %d чанков по %d секунд каждый", numChunks, cs.ChunkSize)

	// Шаблон для выходных файлов
	outputPattern := filepath.Join(tempDir, "chunk_%03d.mp4")

	// Используем ffmpeg с параметром -f segment для эффективного разделения
	segmentCmd := exec.Command("./bin/ffmpeg",
		"-i", tempSourcePath,
		"-c", "copy", // Копируем кодеки без перекодирования
		"-map", "0", // Копируем все потоки
		"-segment_time", fmt.Sprintf("%d", cs.ChunkSize), // Длительность сегмента в секундах
		"-f", "segment", // Формат сегментированного вывода
		"-reset_timestamps", "1", // Сбрасываем таймстемпы для каждого сегмента
		"-avoid_negative_ts", "1", // Предотвращаем отрицательные таймстемпы
		outputPattern)

	if err := segmentCmd.Run(); err != nil {
		log.Printf("Ошибка разделения видео на чанки: %v", err)
		return nil, errors.New(err.Error())
	}

	// Собираем пути ко всем созданным чанкам
	chunkPaths := make([]string, 0, numChunks)
	files, err := os.ReadDir(tempDir)
	if err != nil {
		log.Printf("Ошибка чтения директории с чанками: %v", err)
		return nil, errors.New(err.Error())
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasPrefix(file.Name(), "chunk_") {
			chunkPath := filepath.Join(tempDir, file.Name())
			chunkPaths = append(chunkPaths, chunkPath)
		}
	}

	// Сортируем чанки по имени для правильного порядка
	// sort.Strings(chunkPaths)

	log.Printf("Создано %d чанков", len(chunkPaths))

	// Удаляем временный исходный файл
	os.Remove(tempSourcePath)

	// how to convert []strings to []byte?
	return []byte(strings.Join(chunkPaths, ",")), nil
}
