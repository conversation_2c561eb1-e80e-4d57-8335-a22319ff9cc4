package scheduler

import (
	"container/heap"
	"fmt"

	"github.com/iqhater/video_auto_proccessing_pipeline/internal/task"
	tsk "github.com/iqhater/video_auto_proccessing_pipeline/internal/task"

	"github.com/google/uuid"
)

var _ heap.Interface = (*PriorityQueue)(nil)

// TaskHeap is something we manage in a priority queue.
type TaskHeap struct {
	*task.Task
}

func NewTaskHeap(title string, priority int, opts map[string]any) *TaskHeap {
	return &TaskHeap{
		Task: &task.Task{
			ID:       uuid.New(),
			Title:    title,
			Priority: priority,
			Opts:     opts,
			Status:   task.StatusPending,
			Index:    -1,
		},
	}
}

func (pq *PriorityQueue) AddToHeap(task *tsk.Task) {
	taskHeap := &TaskHeap{
		Task: task,
	}
	heap.Push(pq, taskHeap)
}

// A PriorityQueue implements heap.Interface and holds Items.
type PriorityQueue []*TaskHeap

func (pq PriorityQueue) Len() int { return len(pq) }

func (pq PriorityQueue) Less(i, j int) bool {
	// We want Pop to give us the highest, not lowest, priority so we use greater than here.
	return pq[i].Priority > pq[j].Priority
}

func (pq PriorityQueue) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
	pq[i].Index = i
	pq[j].Index = j
}

func (pq *PriorityQueue) Push(x any) {
	n := len(*pq)
	item := x.(*TaskHeap)
	item.Index = n
	*pq = append(*pq, item)
}

func (pq *PriorityQueue) Pop() any {
	old := *pq
	n := len(old)
	item := old[n-1]
	old[n-1] = nil  // don't stop the GC from reclaiming the item eventually
	item.Index = -1 // for safety
	*pq = old[0 : n-1]
	return item
}

// update modifies the priority and value of an Item in the queue.
func (pq *PriorityQueue) Update(item *TaskHeap, value string, priority int) {
	item.Title = value
	item.Priority = priority
	heap.Fix(pq, item.Index)
}

func InitHeap(tasks []*TaskHeap) error {

	// Create a priority queue, put the items in it, and
	// establish the priority queue (heap) invariants.
	pq := make(PriorityQueue, len(tasks))
	i := 0
	for _, task := range tasks {
		pq[i] = &TaskHeap{
			// NewTask(task.Title, task.Priority, task.Opts),
			Task: &tsk.Task{
				ID:       task.ID,
				Title:    task.Title,
				Priority: task.Priority,
				Opts:     task.Opts,
				Status:   task.Status,
				Index:    i,
			},
			// Title:    task.Title,
			// Priority: task.Priority,
			// Index:    i,
		}
		i++
	}
	heap.Init(&pq)

	// Insert a new item and then modify its priority.
	/* task := &TaskHeap{
			Title:    "task6",
			Priority: 1,
		}
		heap.Push(&pq, task)
	  pq.Update(task, task.Title, 5) */

	// Take the items out; they arrive in decreasing priority order.
	for pq.Len() > 0 {
		item := heap.Pop(&pq).(*TaskHeap)
		fmt.Printf("Priority:%.2d:Title:%s ", item.Priority, item.Title)
	}

	return nil
}
