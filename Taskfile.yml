version: '3'

# Global environment variables
env:

  # Go parameters
  GOBUILD: go build
  PLATFORM: windows.exe

  MODULE_NAME: video_auto_editor

  # Binary name
  BINARY_NAME: video_auto_editor

  # App entrypoint path
  APP_ENTRYPOINT: ./cmd/app

# Global variables
vars:
  GO_VERSION: '1.24.4'
  GOLANGCI_LINT_VERSION: 'v2.2.1'
  GCI_VERSION: 'v0.13.6'
  GOFUMPT_VERSION: 'v0.8.0'

  BIN_DIR: '{{.ROOT_DIR}}/bin'
  GOLANGCI_LINT: '{{.BIN_DIR}}/golangci-lint'
  GCI: '{{.BIN_DIR}}/gci'
  GOFUMPT: '{{.BIN_DIR}}/gofumpt'

tasks:
  
  # Debug for test Taskfile output
  debug:
    cmds:
      - echo "⚙️ Debug info"
      - if [ $(go env GOOS) == "windows" ]; then echo "Windows OK!"; fi

  # Install dependencies
  install:
    silent: true
    cmds:
      - echo "📦 Install Dependencies"
      - go mod tidy && go mod verify
      - echo "📦 Install External Dependencies"
      - task: install:ffmpeg
      - task: check:ffmpeg
      - task: install-formatters
      - task: install-golangci-lint
      # - task: install:whisper

  install:ffmpeg:
    cmds:
      - echo "Installing FFmpeg"
      - if [ ! -f "{{.BIN_DIR}}/ffmpeg.exe" ]; then
          echo "Downloading FFmpeg...";
          mkdir -p {{.BIN_DIR}};
          curl -L https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip -o ffmpeg.zip;
          unzip -o ffmpeg.zip -d ./temp;
          cp ./temp/ffmpeg-master-latest-win64-gpl/bin/ffmpeg.exe {{.BIN_DIR}}/;
          cp ./temp/ffmpeg-master-latest-win64-gpl/bin/ffprobe.exe {{.BIN_DIR}}/;
          rm -rf ./temp ffmpeg.zip;
          echo "FFmpeg installed to {{.BIN_DIR}} directory";
        fi
      - echo "✅ FFmpeg installation completed"

  check:ffmpeg:
    cmds:
      - echo "Checking FFmpeg installation..."
      - |
        if [ -f "{{.BIN_DIR}}/ffmpeg.exe" ]; then
          echo "✅ FFmpeg found in {{.BIN_DIR}}"
        elif command -v ffmpeg >/dev/null 2>&1; then
          echo "✅ FFmpeg found in PATH"
        else
          echo "❌ FFmpeg not found"
          exit 1
        fi

  # install:whisper:
  #   cmds:
  #     - echo "Installing Whisper"
  #     - git clone https://github.com/openai/whisper.git
  #     - cd whisper && pip install -e .

  install-formatters:
    desc: "Устанавливает форматтеры gci и gofumpt в ./bin"
    summary: |
      Эта задача проверяет наличие инструментов форматирования кода gofumpt и gci в директории bin.
      Если инструменты не найдены, они будут автоматически установлены с указанными версиями.
      
      Используется:
        - gofumpt: для форматирования кода Go
        - gci: для сортировки импортов Go
    cmds:
      - |
        [ -f {{.GOFUMPT}} ] || {
          echo '📦 Устанавливаем gofumpt {{.GOFUMPT_VERSION}}...'
          GOBIN={{.BIN_DIR}} go install mvdan.cc/gofumpt@{{.GOFUMPT_VERSION}}
        }
        [ -f {{.GCI}} ] || {
          echo '📦 Устанавливаем gci {{.GCI_VERSION}}...'
          GOBIN={{.BIN_DIR}} go install github.com/daixiang0/gci@{{.GCI_VERSION}}
        }
    status:
      - test -x {{.GOFUMPT}}
      - test -x {{.GCI}}

  install-golangci-lint:
    desc: "Устанавливает golangci-lint в каталог bin"
    summary: |
      Проверяет наличие golangci-lint в директории bin.
      Если инструмент не найден, автоматически устанавливает его через go install.
      
      Устанавливаемая версия: {{.GOLANGCI_LINT_VERSION}}
    cmds:
      - |
        [ -f {{.GOLANGCI_LINT}} ] || {
          mkdir -p ./bin
          echo "📦 Устанавливаем golangci-lint {{.GOLANGCI_LINT_VERSION}}..."
          GOBIN={{.BIN_DIR}} go install github.com/golangci/golangci-lint/v2/cmd/golangci-lint@{{.GOLANGCI_LINT_VERSION}}
        }
    status:
      - test -x {{.GOLANGCI_LINT}}

  # Build app
  build:
    silent: true
    cmds:
      - $GOBUILD -o ${BINARY_NAME}_${PLATFORM} $APP_ENTRYPOINT
      - echo "📦 Build Done"

  # Build api docs
  build:docs:
    silent: true
    cmds:
      - $GOBUILD -o ${BINARY_NAME}_${PLATFORM} $API_DOCS_ENTRYPOINT
      - echo "📦 Build Done"

  # Build app for all platforms
  build-all:
    cmds:
      - for: ["darwin", "linux", "windows.exe"]
        cmd: $GOBUILD -o ${BINARY_NAME}_{{ .ITEM }} $APP_ENTRYPOINT
      - echo "📦 Builds Done"

  # Build and run
  run:
    silent: true
    deps: [build]
    cmds:
      - echo "🚀 Running App"
      - ./${BINARY_NAME}_${PLATFORM}

  # Build and run API docs server
  run:docs:
    silent: true
    deps: [build:docs]
    cmds:
      - echo "🚀 Running API Docs Server"
      - ./${BINARY_NAME}_${PLATFORM}

  # Build Docker Image
  # build:docker:
  #   silent: true
  #   cmds:
  #     - echo "📦 Build & Tag Docker Image"
  #     - docker build -t ${BINARY_NAME}_local ./deployments/.
  #     - docker tag ${BINARY_NAME}_local:latest ${BINARY_NAME}_local:staging
  #     - docker-compose -f ./deployments/docker-compose.yml up -d

  # Run in docker
  # run:docker:
  #   silent: true
  #   deps: [build:docker]
  #   cmds:
  #     - echo "🚀 Running App in Docker"
  #     - docker-compose -f ./deployments/docker-compose.yml up -d

  # Clean app binaries
  clean:
    cmds:
      - echo "🧹 Clean App Binaries"
      - for: ["darwin", "linux", "windows"]
        cmd: if [ $(go env GOOS) == {{ .ITEM }} ]; then rm ${BINARY_NAME}_${PLATFORM}; fi 

  # Run unit tests
  test:
    silent: true
    # deps: [mock]
    cmds:
      - echo "🧪 Running Unit Tests" 
      - go test -v -race -cover -count=1 ./...
      
      # clear temp test files. comment if you want to keep them
      # - rm -rf ./test/temp/**

  # Run e2e tests
  # test:e2e:
  #   silent: true
  #   cmds:
  #     - echo "🧪 Running e2e Tests" 
  #     - go test -v -race -cover -count=1 ./tests/e2e/...

  # Generate mocks for tests
  # mock:
  #   silent: true
  #   cmds:
  #     - echo "🧪 Generating Mocks"
  #     # For some reason .mockery.yaml config file does not work :(
  #     # - mockery --with-expecter=true --all --output ./internal/mocks
  #     - mockery
      
  # Lint
  lint:
    silent: true
    deps: [ install-golangci-lint ]
    vars:
      MODULES: '{{.MODULES}}'
      GOLANGCI_LINT: '{{.GOLANGCI_LINT}}'
    cmds:
      - echo "🔦 Running Code Linter"
        {{.GOLANGCI_LINT}} run $mod/... --config=.golangci.yml || ERR=1
      # - |
      #   set -e
      #   ERR=0
      #   echo "🔍 Линтим все модули ..."
      #   for mod in {{.MODULES}}; do
      #     if [ -d "$mod" ]; then
      #       echo "🔍 Линтим $mod module"
      #       {{.GOLANGCI_LINT}} run $mod/... --config=.golangci.yml || ERR=1
      #     fi
      #   done
      #   exit $ERR