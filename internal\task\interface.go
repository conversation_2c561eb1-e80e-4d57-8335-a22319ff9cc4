/*
Содер<PERSON><PERSON><PERSON> всё, что связано с описанием и жизненным циклом задач:

TaskDefinition → шаблоны задач, параметры, зависимости.

TaskInstance → инстансы с FSM, контекстом и ID.

FSM (fsm.go) — управляет состояниями внутри TaskInstance.

DependencyGraph (task_graph.go) — хранит узлы и рёбра зависимостей, определяет готовые к запуску инстансы.
*/
package task

import (
	"context"

	"github.com/google/uuid"
)

type Tasker interface {

	// Actions
	Run(ctx context.Context) error
	Stop(ctx context.Context) error

	// CRUD
	AddTask(title string, opts map[string]any)
	FindTaskByName(taskName string) *Task
	FindTaskByID(taskID uuid.UUID) *Task
	GetTasks() []*Task
	DeleteTaskByID(taskID uuid.UUID)
}
