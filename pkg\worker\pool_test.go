package worker

import (
	"testing"
	"time"
)

func TestWorkerPoolOK(t *testing.T) {

	// Arrange
	totalJobs := 10
	totalWorkers := 5

	jobFunc1 := func() any {
		time.Sleep(50 * time.Millisecond)
		return 42
	}

	jobFunc2 := func() any {
		time.Sleep(100 * time.Millisecond)
		return 42
	}

	jobFunc3 := func() any {
		time.Sleep(150 * time.Millisecond)
		return 42
	}

	jobFuncs := []func() any{jobFunc1, jobFunc2, jobFunc3}

	var results []any
	handleResults := func(res any) {
		results = append(results, res)
	}

	// Act
	WorkerPool(totalJobs, totalWorkers, jobFuncs, handleResults)

	// Assert
	if len(results) != totalJobs {
		t.<PERSON>rrorf("Expected %d results, got %d", totalJobs, len(results))
	}
	for _, res := range results {
		if res != 42 {
			t.<PERSON><PERSON><PERSON>("Expected result to be 42, got %v", res)
		}
	}
}

func TestWorkerOK(t *testing.T) {

	// Arrange
	jobs := make(chan any, 2)
	results := make(chan any, 2)

	jobFunc := func() any {
		time.Sleep(100 * time.Millisecond)
		return 42
	}

	go func() {
		jobs <- 1
		jobs <- 2
		close(jobs)
	}()

	// Act
	for i := 1; i <= 5; i++ {
		go worker(i, jobs, results, jobFunc)
	}
	// go worker(1, jobs, results, jobFunc)
	// go worker(2, jobs, results, jobFunc)
	// go worker(3, jobs, results, jobFunc)

	res1 := <-results
	res2 := <-results

	// Assert
	if res1 != 42 || res2 != 42 {
		t.Errorf("Expected results to be 42, got %v and %v", res1, res2)
	}
}
