package config

import "encoding/json"

var _ Parser = (*JSONParser)(nil)

type TaskJSONConfig struct {
	Name   string         `json:"name"`
	Params map[string]any `json:"params"`
	// Opts   map[string]any `json:"opts"`
}

type TasksJSONWrapper struct {
	Tasks []*TaskJSONConfig `json:"tasks"`
}

type ChunkJSONConfig struct {
	ChunkSizeMB int               `json:"chunk_size_mb"`
	Tasks       []*TaskJSONConfig `json:"tasks"`
}

type ProcessingJSONConfig struct {
	Pre   *TasksJSONWrapper `json:"pre"`
	Chunk *ChunkJSONConfig  `json:"chunk"`
	Post  *TasksJSONWrapper `json:"post"`
}

type J<PERSON>NParser struct {
	Input                string `json:"video_input"`
	Output               string `json:"video_output"`
	ProcessingJSONConfig `json:"processing"`
}

func (p *JSONParser) Parse(config []byte) error {
	if err := json.Unmarshal(config, &p); err != nil {
		return err
	}
	return nil
}
