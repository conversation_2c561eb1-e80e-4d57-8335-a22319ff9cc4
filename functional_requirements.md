
# Функциональные требования

Ниже представлен список функциональных требований для проекта автоматической обработки видео. Отмечайте выполненные требования, устанавливая галочку в соответствующем чекбоксе.

## Список требований

| №  | Требование       | Описание                                 | Приоритет | Статус |
|----|------------------|------------------------------------------|-----------|--------|
| 1  | **Обработка аудио** | Приложение должно обрабатывать аудиодорожку видео: нормализация, компрессия, де-эссер | 🔴 Высокий | [ ]    |
| 2  | **Удаление слов-паразитов** | Приложение должно обнаруживать и удалять слова-паразиты ("ээ", "мм" и т.д.) | 🔴 Высокий | [ ]    |
| 3  | **Создание таймкодов** | Приложение должно создавать таймкоды на основе транскрипции речи | 🔴 Высокий | [ ]    |
| 4  | **Обрезка пустых моментов** | Приложение должно обнаруживать и обрезать моменты без речи и действия | 🟠 Средний | [ ]    |
| 5  | **Замена невнятной речи** | Приложение должно заменять невнятную речь синтезированной с помощью TTS | 🟠 Средний | [ ]    |
| 6  | **Мониторинг директории** | Приложение должно отслеживать появление новых файлов в директории input | 🔴 Высокий | [x]    |
| 7  | **Параллельная обработка** | Приложение должно использовать конкурентность Go для параллельной обработки нескольких файлов | 🟠 Средний | [x]    |
| 8  | **Сохранение обработанных файлов** | Приложение должно сохранять обработанные файлы в директорию output с добавлением "_processed" к имени | 🔴 Высокий | [ ]    |
| 9  | **Настройка порога шума** | Приложение должно иметь настраиваемый порог для фоновой музыки, чтобы не затрагивать её при обработке голоса | 🟠 Средний | [ ]    |
| 10 | **Graceful shutdown** | Приложение должно корректно завершать работу при получении сигнала завершения | 🟢 Низкий | [x]    |

## Детальное описание требований

### 1. Обработка аудио

- **Описание**: Приложение должно выполнять комплексную обработку аудиодорожки видео.
- **Критерии приёмки**:
  - [ ] Нормализация громкости для обеспечения равномерного уровня звука
  - [ ] Компрессия для улучшения разборчивости речи
  - [ ] Де-эссер для устранения шипящих звуков
  - [ ] Сохранение качества голоса при обработке
- **Приоритет**: 🔴 Высокий

### 2. Удаление слов-паразитов

- **Описание**: Приложение должно обнаруживать и удалять слова-паразиты из речи.
- **Критерии приёмки**:
  - [ ] Обнаружение слов-паразитов ("ээ", "эээ", "ммм", "эм", "как бы", "типа", "ну", "вот")
  - [ ] Удаление слов-паразитов без нарушения плавности речи
- **Приоритет**: 🔴 Высокий

### 3. Создание таймкодов

- **Описание**: Приложение должно создавать таймкоды на основе транскрипции речи.
- **Критерии приёмки**:
  - [ ] Транскрипция аудио с помощью Whisper
  - [ ] Создание таймкодов в формате ЧЧ:ММ:СС с соответствующим текстом
  - [ ] Сохранение таймкодов в отдельный файл
- **Приоритет**: 🔴 Высокий

### 4. Обрезка пустых моментов

- **Описание**: Приложение должно обнаруживать и обрезать моменты без речи и действия.
- **Критерии приёмки**:
  - [ ] Обнаружение сцен без речи и значимого действия
  - [ ] Обрезка пустых моментов без нарушения контекста
  - [ ] Сохранение плавности переходов между сценами
- **Приоритет**: 🟠 Средний

### 5. Замена невнятной речи

- **Описание**: Приложение должно заменять невнятную речь синтезированной с помощью TTS.
- **Критерии приёмки**:
  - [ ] Обнаружение невнятной речи в аудиодорожке
  - [ ] Генерация чистой речи с помощью TTS (Coqui или аналог)
  - [ ] Бесшовная интеграция синтезированной речи в аудиодорожку
- **Приоритет**: 🟠 Средний

### 6. Мониторинг директории

- **Описание**: Приложение должно отслеживать появление новых файлов в директории input.
- **Критерии приёмки**:
  - [x] Обнаружение новых файлов в директории input
  - [x] Автоматический запуск обработки при появлении нового файла
  - [x] Обработка существующих файлов при запуске приложения
- **Приоритет**: 🔴 Высокий

### 7. Параллельная обработка

- **Описание**: Приложение должно использовать конкурентность Go для параллельной обработки нескольких файлов.
- **Критерии приёмки**:
  - [x] Использование горутин для параллельной обработки
  - [x] Эффективное управление ресурсами при параллельной обработке
  - [x] Масштабируемость системы при увеличении количества файлов
- **Приоритет**: 🟠 Средний

### 8. Сохранение обработанных файлов

- **Описание**: Приложение должно сохранять обработанные файлы в директорию output с добавлением "_processed" к имени.
- **Критерии приёмки**:
  - [ ] Сохранение обработанных файлов в директорию output
  - [ ] Добавление суффикса "_processed" к имени файла
  - [ ] Сохранение качества видео при обработке
- **Приоритет**: 🔴 Высокий

### 9. Настройка порога шума

- **Описание**: Приложение должно иметь настраиваемый порог для фоновой музыки, чтобы не затрагивать её при обработке голоса.
- **Критерии приёмки**:
  - [ ] Настраиваемый порог для фоновой музыки (в дБ)
  - [ ] Сохранение фоновой музыки при обработке голоса
  - [ ] Четкое разделение голоса и фоновой музыки
- **Приоритет**: 🟠 Средний

### 10. Graceful shutdown

- **Описание**: Приложение должно корректно завершать работу при получении сигнала завершения.
- **Критерии приёмки**:
  - [x] Обработка сигналов SIGINT и SIGTERM
  - [x] Завершение текущих задач перед выходом
  - [x] Освобождение ресурсов при завершении работы
- **Приоритет**: 🟢 Низкий

## История изменений

- **Дата**: 2024-05-15
  - Создан первоначальный список требований для проекта автоматической обработки видео.
  - Определены приоритеты и критерии приёмки для каждого требования.
