<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg" font-family="Arial, Helvetica, sans-serif">
  <!-- Background -->
  <rect width="100%" height="100%" fill="#f9f9f9"></rect>

  <!-- TaskDefinition -->
  <rect x="50" y="40" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="150" y="70" text-anchor="middle" font-size="16" fill="#000">TaskDefinition</text>

  <!-- TaskInstance -->
  <rect x="300" y="40" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="400" y="70" text-anchor="middle" font-size="16" fill="#000">TaskInstance</text>

  <!-- DependencyGraph -->
  <rect x="50" y="140" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="150" y="170" text-anchor="middle" font-size="16" fill="#000">DependencyGraph</text>

  <!-- Scheduler -->
  <rect x="300" y="140" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="400" y="170" text-anchor="middle" font-size="16" fill="#000">Scheduler</text>

  <!-- Executor & WorkerPool -->
  <rect x="300" y="240" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="400" y="270" text-anchor="middle" font-size="16" fill="#000">Executor &amp; WorkerPool</text>

  <!-- FSM -->
  <rect x="300" y="340" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="400" y="370" text-anchor="middle" font-size="16" fill="#000">FSM</text>

  <!-- EventBus -->
  <rect x="300" y="440" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="400" y="470" text-anchor="middle" font-size="16" fill="#000">EventBus</text>

  <!-- Collector -->
  <rect x="550" y="440" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="650" y="470" text-anchor="middle" font-size="16" fill="#000">Collector</text>

  <!-- ResourceManager -->
  <rect x="550" y="140" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="650" y="170" text-anchor="middle" font-size="16" fill="#000">ResourceManager</text>

  <!-- RetryPolicy -->
  <rect x="550" y="240" width="200" height="50" rx="8" ry="8" fill="#ddeeff" stroke="#333" stroke-width="2"></rect>
  <text x="650" y="270" text-anchor="middle" font-size="16" fill="#000">RetryPolicy</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L10,5 L0,10" fill="#333"></path>
    </marker>
  </defs>

  <!-- TaskDefinition -> TaskInstance -->
  <line x1="250" y1="65" x2="300" y2="65" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- TaskDefinition -> DependencyGraph -->
  <line x1="150" y1="90" x2="150" y2="140" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- DependencyGraph -> Scheduler -->
  <line x1="250" y1="165" x2="300" y2="165" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- TaskInstance -> Scheduler -->
  <line x1="400" y1="90" x2="400" y2="140" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- Scheduler -> Executor & WorkerPool -->
  <line x1="400" y1="190" x2="400" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- Executor & WorkerPool -> FSM -->
  <line x1="400" y1="290" x2="400" y2="340" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- FSM -> EventBus -->
  <line x1="400" y1="390" x2="400" y2="440" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- EventBus -> Collector -->
  <line x1="500" y1="465" x2="550" y2="465" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- Scheduler -> ResourceManager -->
  <line x1="500" y1="165" x2="550" y2="165" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>

  <!-- Scheduler -> RetryPolicy -->
  <line x1="500" y1="265" x2="550" y2="265" stroke="#333" stroke-width="2" marker-end="url(#arrow)"></line>
</svg>
