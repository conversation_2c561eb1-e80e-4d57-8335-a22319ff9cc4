# Conceptual Abstructions App Schema

<img src="abstructions_schema.svg" alt="Abstractions Schema" width="800">

* **TaskDefinition**
  Описывает «шаблон» задачи: её имя, входные/выходные форматы, параметры (битрейт, разрешение и т. д.) и зависимости. Всё, что неизменно для одинаковых задач, хранится здесь.

* **TaskInstance**
  Живая копия `TaskDefinition` в рамках одного запуска конвейера. Хранит текущее состояние (`Pending`, `Running`, `Completed`, `Failed`), результаты, логи и метрики.

* **DependencyGraph**
  Представляет задачи как узлы DAG (ориентированного ациклического графа). Рёбра задают, какие задачи должны выполниться раньше других. Благодаря графу можно легко ветвить обработку и автоматически распараллеливать независимые ветви.

* **Scheduler**
  Оркестратор, который:

  1. Считывает `DependencyGraph` и текущее состояние всех `TaskInstance`.
  2. Решает, какие инстансы готовы к запуску (все зависимости соблюдены).
  3. Учитывает ограничения `ResourceManager` (например, лимит CPU) и `RetryPolicy` (политику повторных попыток).

* **ResourceManager**
  Следит за нагрузкой на систему (CPU, память, I/O) и ограничивает число «тяжёлых» задач одновременно. Защищает от перегрузок и помогает равномерно распределять ресурсы.

* **RetryPolicy**
  Правила повторных попыток при временных ошибках: фиксированная задержка, экспоненциальный бэк-офф, максимальное число попыток. `Scheduler` использует эту политику, чтобы не «залипать» на упавшей задаче.

* **Executor & WorkerPool**

  * **Executor** запускает конкретный `TaskInstance` в горутине, инжектит нужный `ProcessingStrategy` и следит за контекстом (`context.Context`).
  * **WorkerPool** ограничивает число одновременно активных горутин, чтобы не выжать все ресурсы машины.

* **FSM (Finite State Machine)**
  Контролирует жизненный цикл каждого `TaskInstance`:

  ```
  Pending → Running → (Completed | Failed → Retrying → Completed)  
                                ↑  
                            Cancelled  
  ```

  FSM гарантирует корректные переходы и позволяет легко встраивать колл-беки на смену состояний.

* **EventBus**
  Шина событий, через которую `Executor` и `FSM` публикуют события (`TaskStarted`, `TaskProgress`, `TaskCompleted`, `TaskFailed`). Подписчики (логирование, UI, метрики) получают обновления и реагируют.

* **Collector**
  Собирает и агрегирует результаты из разных ветвей графа. Ждёт, пока все необходимые `TaskInstance` перейдут в состояние `Completed`, а затем формирует финальный выходной артефакт (обработанный аудио- или видеофайл).

---

**Стрелки на схеме**

* От **TaskDefinition** к **TaskInstance**: порождение живого объекта из шаблона.
* От **TaskDefinition** к **DependencyGraph**: информация о порядке и зависимостях уходит в граф.
* От **DependencyGraph** и **TaskInstance** к **Scheduler**: `Scheduler` получает полную картину графа и состояния инстансов.
* От **Scheduler** к **Executor & WorkerPool**: готовые к запуску задачи отправляются на исполнение.
* От **Executor & WorkerPool** к **FSM**: старт и окончание работы задачи фиксируются в FSM.
* От **FSM** к **EventBus**: любые изменения состояния публикуются в шину.
* От **EventBus** к **Collector**: события успешного завершения уведомляют Collector, что можно начать агрегацию результатов.
* От **Scheduler** к **ResourceManager** и **RetryPolicy**: `Scheduler` запрашивает эти сервисы для принятия решения о запуске задач и их повторных попытках.
