package worker

import (
	"fmt"
)

/* Usage example:

const totalJobs = 50
const totalWorkers = 100

r := rand.New(rand.NewSource(time.Now().UnixNano()))

jobFunc := func() any {
	time.Sleep(time.Duration(r.Intn(10)) * time.Second)
	return 6 * r.Intn(8)
}

handleResults := func(result any) {
	if result == 42 {
		fmt.Println("Got 42!", result)
	}
}

WorkerPool(totalJobs, totalWorkers, jobFunc, handleResults)

*/

func WorkerPool(totalJobs, totalWorkers int, jobFuncs []func() any, handleResults func(any)) {

	jobs := make(chan any, totalJobs)
	results := make(chan any, totalJobs)

	for w := 1; w <= totalWorkers; w++ {
		go worker(w, jobs, results, jobFuncs[w-1])
	}

	// Send jobs
	for j := 1; j <= totalJobs; j++ {
		jobs <- j
	}
	close(jobs)

	// Receive results
	for a := 1; a <= totalJobs; a++ {

		res := <-results

		// filter or handle results function
		handleResults(res)
		// fmt.Println("Total Progress: ", urls.GetTotalProgress(res))
	}
	close(results)
}

func worker(id int, jobs <-chan any, results chan<- any, jobFunc func() any) {

	for job := range jobs {

		go func() {
			fmt.Printf("Worker %d started job %d\n", id, job)

			// Do work and send result
			result := jobFunc()
			results <- result

			fmt.Printf("Worker %d finished job %d\n", id, job)
		}()
	}
}
