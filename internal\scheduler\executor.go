package scheduler

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/iqhater/video_auto_proccessing_pipeline/internal/task"
	tsk "github.com/iqhater/video_auto_proccessing_pipeline/internal/task"
)

type Executor interface {
	Run(ctx context.Context, task *task.Task) error
}

var _ Executor = (*ExecutorImpl)(nil)

type ExecutorImpl struct {
	task.Tasker
	// PriorityQueue
}

func (p *ExecutorImpl) Run(ctx context.Context, task *tsk.Task) error {

	// simulate task proccessing
	delaySimulation := time.Millisecond * time.Duration(rand.Intn(1000))
	fmt.Printf("Proccessing task: %s\n", delaySimulation)
	task.Status = tsk.StatusRunning

	// simulate success tasks
	if delaySimulation >= 350*time.Millisecond {
		task.Status = tsk.StatusFailed
		return fmt.Errorf("task failed, id: %s", task.ID.String())
	}

	task.Status = tsk.StatusCompleted
	return nil
}

func NewExecutor(tasker task.Tasker) *ExecutorImpl {
	return &ExecutorImpl{
		Tasker: tasker,
	}
}
