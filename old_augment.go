/* package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/fsnotify/fsnotify"
)

// Глобальные переменные для путей к исполняемым файлам
var (
	ffmpegPath  string
	whisperPath string
	ttsPath     string
)

func init() {
	// Проверяем, есть ли ffmpeg в PATH
	if path, err := exec.LookPath("ffmpeg"); err == nil {
		ffmpegPath = path
	} else {
		// Если нет, используем путь по умолчанию
		// Измените на свой путь к ffmpeg
		ffmpegPath = "C:\\ffmpeg\\bin\\ffmpeg.exe"
	}

	// Аналогично для whisper и tts
	if path, err := exec.LookPath("whisper"); err == nil {
		whisperPath = path
	} else {
		whisperPath = "C:\\whisper\\whisper.exe" // Измените на свой путь
	}

	if path, err := exec.LookPath("tts"); err == nil {
		ttsPath = path
	} else {
		ttsPath = "C:\\tts\\tts.exe" // Измените на свой путь
	}

	// Проверяем существование файлов
	if _, err := os.Stat(ffmpegPath); os.IsNotExist(err) {
		log.Printf("Предупреждение: FFmpeg не найден по пути %s", ffmpegPath)
	}
	if _, err := os.Stat(whisperPath); os.IsNotExist(err) {
		log.Printf("Предупреждение: Whisper не найден по пути %s", whisperPath)
	}
	if _, err := os.Stat(ttsPath); os.IsNotExist(err) {
		log.Printf("Предупреждение: TTS не найден по пути %s", ttsPath)
	}
}

// Структура для чтения JSON от whisper.cpp
type WhisperResult struct {
	Segments []struct {
		Text  string  `json:"text"`
		Start float64 `json:"start"`
		End   float64 `json:"end"`
	} `json:"segments"`
}

// Настройки обработки аудио
type AudioProcessingConfig struct {
	// Порог для фоновой музыки (дБ)
	NoiseFloor float64
	// Настройки компрессора
	Threshold   float64
	Ratio       float64
	AttackTime  float64
	ReleaseTime float64
	// Настройки де-эссера
	DeEsserFreq   int
	DeEsserAmount float64
	// Порог для обнаружения пауз (в секундах)
	SilenceThreshold float64
	// Порог для обнаружения невнятной речи
	ClarityThreshold float64
}

func main() {
	// Создаем необходимые директории
	createRequiredDirs()

	// Канал для файлов на обработку
	fileQueue := make(chan string)
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Запуск watcher
	go watchInputDir(ctx, fileQueue)

	// Запуск worker pool (2 воркера для прототипа)
	for i := 0; i < 2; i++ {
		go worker(ctx, i, fileQueue)
	}

	// Graceful shutdown
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh
	log.Println("Получен сигнал завершения, ожидаем завершения задач...")
	cancel()
	<-ctx.Done()
}

// Создание необходимых директорий
func createRequiredDirs() {
	dirs := []string{"input", "output", "processing", "temp"}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Fatalf("Не удалось создать директорию %s: %v", dir, err)
		}
	}
}

func watchInputDir(ctx context.Context, fileQueue chan<- string) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		log.Fatal(err)
	}
	defer watcher.Close()

	inputDir := "input"
	if err := watcher.Add(inputDir); err != nil {
		log.Fatalf("Не удалось добавить %s в watcher: %v", inputDir, err)
	}

	// Поиск существующих файлов при старте
	files, _ := filepath.Glob(filepath.Join(inputDir, "*.mp4"))
	for _, f := range files {
		fileQueue <- f
	}

	for {
		select {
		case <-ctx.Done():
			return
		case ev := <-watcher.Events:
			if ev.Op&fsnotify.Create != 0 && filepath.Ext(ev.Name) == ".mp4" {
				log.Printf("Найден новый файл: %s", ev.Name)
				fileQueue <- ev.Name
			}
		case err := <-watcher.Errors:
			log.Printf("Watcher error: %v", err)
		}
	}
}

func worker(ctx context.Context, id int, fileQueue <-chan string) {
	for {
		select {
		case <-ctx.Done():
			return
		case file := <-fileQueue:
			log.Printf("Worker %d начал обработку: %s", id, file)
			err := processFile(ctx, file)
			if err != nil {
				log.Printf("Ошибка обработки %s: %v", file, err)
			} else {
				log.Printf("Worker %d завершил %s", id, file)
			}
		}
	}
}

// processFile выполняет полный конвейер обработки видео
func processFile(ctx context.Context, inputPath string) error {
	base := strings.TrimSuffix(filepath.Base(inputPath), ".mp4")
	jsonOut := filepath.Join("processing", base+".json")
	tempDir := filepath.Join("temp", base)

	// Создаем временную директорию для файла
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("создание временной директории: %v", err)
	}
	defer os.RemoveAll(tempDir) // Очистка временных файлов после обработки

	// Извлекаем аудио из видео
	audioPath := filepath.Join(tempDir, "audio.wav")
	if err := extractAudio(ctx, inputPath, audioPath); err != nil {
		return fmt.Errorf("извлечение аудио: %v", err)
	}

	// 1. Транскрипция (whisper.cpp)
	whisperCmd := exec.CommandContext(ctx, whisperPath, audioPath,
		"--model", "small", "--output_format", "json", "--output_dir", "processing")
	if out, err := whisperCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("whisper error: %v, output: %s", err, string(out))
	}
	log.Printf("Транскрипция завершена: %s", jsonOut)

	// Парсинг JSON для следующего шага
	data, err := os.ReadFile(jsonOut)
	if err != nil {
		return fmt.Errorf("чтение JSON: %v", err)
	}
	var res WhisperResult
	if err := json.Unmarshal(data, &res); err != nil {
		return fmt.Errorf("парсинг JSON: %v", err)
	}
	log.Printf("Найдено сегментов: %d", len(res.Segments))

	// 2. Анализ транскрипта и создание таймкодов
	timecodes, problematicSegments := analyzeTranscript(res)

	// Сохраняем таймкоды в файл
	timecodePath := filepath.Join("output", base+"_timecodes.txt")
	if err := saveTimecodes(timecodes, timecodePath); err != nil {
		return fmt.Errorf("сохранение таймкодов: %v", err)
	}

	// 3. Обработка аудио
	processedAudioPath := filepath.Join(tempDir, "processed_audio.wav")
	if err := processAudio(ctx, audioPath, processedAudioPath); err != nil {
		return fmt.Errorf("обработка аудио: %v", err)
	}

	// 4. Переозвучка проблемных сегментов
	if len(problematicSegments) > 0 {
		if err := fixProblematicSegments(ctx, processedAudioPath, problematicSegments, tempDir); err != nil {
			return fmt.Errorf("переозвучка проблемных сегментов: %v", err)
		}
	}

	// 5. Обрезка пустых моментов
	trimmedVideoPath := filepath.Join(tempDir, "trimmed_video.mp4")
	if err := trimSilentScenes(ctx, inputPath, trimmedVideoPath); err != nil {
		return fmt.Errorf("обрезка пустых моментов: %v", err)
	}

	// 6. Сборка видео через ffmpeg
	outputPath := filepath.Join("output", base+"_processed.mp4")
	if err := mergeAudioVideo(ctx, trimmedVideoPath, processedAudioPath, outputPath); err != nil {
		return fmt.Errorf("сборка видео: %v", err)
	}

	return nil
}

// Извлечение аудио из видео
func extractAudio(ctx context.Context, videoPath, audioPath string) error {
	cmd := exec.CommandContext(ctx, ffmpegPath, "-i", videoPath, "-q:a", "0", "-map", "a", audioPath)
	return cmd.Run()
}

// Обработка аудио (нормализация, компрессия, де-эссер)
func processAudio(ctx context.Context, inputPath, outputPath string) error {
	// Настройки обработки
	config := AudioProcessingConfig{
		NoiseFloor:       -40.0,
		Threshold:        -20.0,
		Ratio:            4.0,
		AttackTime:       0.01,
		ReleaseTime:      0.2,
		DeEsserFreq:      7500,
		DeEsserAmount:    0.5,
		SilenceThreshold: 0.5,
		ClarityThreshold: 0.3,
	}

	// Создаем сложный фильтр для ffmpeg
	filter := fmt.Sprintf(
		"highpass=f=100,"+
			"lowpass=f=10000,"+
			"compand=attacks=%.2f:decays=%.2f:points=-90/-90 -%.1f/-%.1f -%.1f/-%.1f 0/0:soft-knee=6:gain=2,"+
			"equalizer=f=%d:width_type=h:width=200:g=-%.1f,"+
			"silenceremove=start_periods=1:start_silence=0.1:start_threshold=%.1f,"+
			"dynaudnorm=f=150:g=15:p=1:m=1:s=0",
		config.AttackTime, config.ReleaseTime,
		config.NoiseFloor, config.NoiseFloor*0.8,
		config.Threshold, config.Threshold/config.Ratio,
		config.DeEsserFreq, config.DeEsserAmount*10,
		config.SilenceThreshold,
	)

	cmd := exec.CommandContext(ctx, ffmpegPath,
		"-i", inputPath,
		"-af", filter,
		"-y", outputPath)

	return cmd.Run()
}

// Анализ транскрипта и создание таймкодов
func analyzeTranscript(result WhisperResult) ([]string, []map[string]interface{}) {
	var timecodes []string
	var problematicSegments []map[string]interface{}

	// Создаем таймкоды на основе сегментов
	for i, segment := range result.Segments {
		// Форматируем время в формат ЧЧ:ММ:СС
		startTime := formatTime(segment.Start)

		// Добавляем таймкод
		timecodeText := fmt.Sprintf("%s - %s", startTime, segment.Text)
		timecodes = append(timecodes, timecodeText)

		// Проверяем на проблемные сегменты (содержащие "ээ", "мм" и т.д.)
		if containsFillerWords(segment.Text) || isUnclearSpeech(segment.Text) {
			problematicSegments = append(problematicSegments, map[string]interface{}{
				"index": i,
				"start": segment.Start,
				"end":   segment.End,
				"text":  segment.Text,
			})
		}
	}

	return timecodes, problematicSegments
}

// Форматирование времени в ЧЧ:ММ:СС
func formatTime(seconds float64) string {
	duration := time.Duration(seconds * float64(time.Second))
	h := int(duration.Hours())
	m := int(duration.Minutes()) % 60
	s := int(duration.Seconds()) % 60
	return fmt.Sprintf("%02d:%02d:%02d", h, m, s)
}

// Проверка на слова-паразиты
func containsFillerWords(text string) bool {
	fillerWords := []string{"ээ", "эээ", "ммм", "эм", "как бы", "типа", "ну", "вот"}
	lowerText := strings.ToLower(text)

	for _, word := range fillerWords {
		if strings.Contains(lowerText, word) {
			return true
		}
	}

	return false
}

// Проверка на невнятную речь (упрощенная реализация)
func isUnclearSpeech(text string) bool {
	// Здесь можно реализовать более сложную логику
	// Например, проверка на повторяющиеся слоги, много гласных подряд и т.д.
	return strings.Contains(text, "...") || strings.Contains(text, "???")
}

// Сохранение таймкодов в файл
func saveTimecodes(timecodes []string, outputPath string) error {
	return os.WriteFile(outputPath, []byte(strings.Join(timecodes, "\n")), 0644)
}

// Переозвучка проблемных сегментов с использованием TTS
func fixProblematicSegments(ctx context.Context, audioPath string, segments []map[string]interface{}, tempDir string) error {
	if len(segments) == 0 {
		return nil
	}

	// Создаем временный файл для каждого сегмента
	for i, segment := range segments {
		start := segment["start"].(float64)
		end := segment["end"].(float64)
		text := segment["text"].(string)

		// Очищаем текст от слов-паразитов
		cleanText := cleanFillerWords(text)

		// Генерируем речь с помощью TTS (например, Coqui)
		ttsOutputPath := filepath.Join(tempDir, fmt.Sprintf("tts_segment_%d.wav", i))
		if err := generateTTS(ctx, cleanText, ttsOutputPath); err != nil {
			return err
		}

		// Заменяем сегмент в исходном аудио
		if err := replaceAudioSegment(ctx, audioPath, ttsOutputPath, start, end, tempDir); err != nil {
			return err
		}
	}

	return nil
}

// Очистка текста от слов-паразитов
func cleanFillerWords(text string) string {
	fillerWords := []string{"ээ", "эээ", "ммм", "эм", "как бы", "типа", "ну", "вот"}
	result := text

	for _, word := range fillerWords {
		result = strings.ReplaceAll(result, word, "")
	}

	// Удаляем лишние пробелы
	result = strings.Join(strings.Fields(result), " ")

	return result
}

// Генерация речи с помощью TTS (Coqui или аналог)
func generateTTS(ctx context.Context, text string, outputPath string) error {
	// Здесь должен быть вызов TTS системы
	// Для примера используем простой вызов внешней программы
	cmd := exec.CommandContext(ctx, ttsPath, "--text", text, "--out_path", outputPath)
	return cmd.Run()
}

// Замена сегмента в аудио
func replaceAudioSegment(ctx context.Context, audioPath, segmentPath string, start, end float64, tempDir string) error {
	duration := end - start

	// Создаем временные файлы для частей аудио
	beforePath := filepath.Join(tempDir, "before.wav")
	afterPath := filepath.Join(tempDir, "after.wav")

	// Вырезаем части до и после сегмента
	if err := exec.CommandContext(ctx, ffmpegPath,
		"-i", audioPath,
		"-to", fmt.Sprintf("%.3f", start),
		"-c", "copy",
		beforePath).Run(); err != nil {
		return err
	}

	if err := exec.CommandContext(ctx, ffmpegPath,
		"-i", audioPath,
		"-ss", fmt.Sprintf("%.3f", end),
		"-c", "copy",
		afterPath).Run(); err != nil {
		return err
	}

	// Подгоняем длительность TTS сегмента под оригинал
	adjustedSegmentPath := filepath.Join(tempDir, "adjusted_segment.wav")
	if err := exec.CommandContext(ctx, ffmpegPath,
		"-i", segmentPath,
		"-filter:a", fmt.Sprintf("atempo=%.3f", duration/getTTSDuration(segmentPath)),
		adjustedSegmentPath).Run(); err != nil {
		return err
	}

	// Объединяем все части
	fileList := filepath.Join(tempDir, "filelist.txt")
	fileContent := fmt.Sprintf("file '%s'\nfile '%s'\nfile '%s'",
		beforePath, adjustedSegmentPath, afterPath)

	if err := os.WriteFile(fileList, []byte(fileContent), 0644); err != nil {
		return err
	}

	return exec.CommandContext(ctx, ffmpegPath,
		"-f", "concat",
		"-safe", "0",
		"-i", fileList,
		"-c", "copy",
		"-y", audioPath).Run()
}

// Получение длительности аудио файла
func getTTSDuration(path string) float64 {
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		path)

	output, err := cmd.Output()
	if err != nil {
		return 1.0 // Возвращаем 1 секунду по умолчанию в случае ошибки
	}

	var duration float64
	fmt.Sscanf(string(output), "%f", &duration)
	return duration
}

// Объединение аудио и видео
func mergeAudioVideo(ctx context.Context, videoPath, audioPath, outputPath string) error {
	cmd := exec.CommandContext(ctx, ffmpegPath,
		"-i", videoPath,
		"-i", audioPath,
		"-map", "0:v",
		"-map", "1:a",
		"-c:v", "copy",
		"-c:a", "aac",
		"-b:a", "192k",
		"-shortest",
		"-y", outputPath)

	return cmd.Run()
}

// Обнаружение и обрезка пустых моментов в видео
func trimSilentScenes(ctx context.Context, inputPath, outputPath string) error {
	// Временный файл для списка сцен
	tempDir := filepath.Dir(outputPath)
	scenesFile := filepath.Join(tempDir, "scenes.txt")

	// Обнаружение сцен с помощью ffmpeg
	sceneDetectCmd := exec.CommandContext(ctx, ffmpegPath,
		"-i", inputPath,
		"-filter:v", "select='gt(scene,0.3)',showinfo",
		"-f", "null", "-")

	sceneOutput, err := sceneDetectCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ошибка обнаружения сцен: %v", err)
	}

	// Анализ вывода для определения временных меток сцен
	scenes := parseSceneChanges(string(sceneOutput))

	// Если сцен мало, возвращаем исходное видео
	if len(scenes) < 2 {
		// Просто копируем файл
		copyCmd := exec.CommandContext(ctx, ffmpegPath,
			"-i", inputPath,
			"-c", "copy",
			"-y", outputPath)
		return copyCmd.Run()
	}

	// Создаем файл с сегментами для обрезки
	var segments []string
	for i := 0; i < len(scenes)-1; i++ {
		start := scenes[i]
		end := scenes[i+1]

		// Проверяем, есть ли звук в этом сегменте
		hasSpeech, err := checkForSpeech(ctx, inputPath, start, end)
		if err != nil {
			log.Printf("Ошибка проверки речи: %v", err)
			hasSpeech = true // В случае ошибки считаем, что речь есть
		}

		// Если сегмент содержит речь или короткий, добавляем его
		if hasSpeech || (end-start < 2.0) {
			segment := fmt.Sprintf("file '%s'\ninpoint %.3f\noutpoint %.3f",
				inputPath, start, end)
			segments = append(segments, segment)
		}
	}

	// Записываем сегменты в файл
	err = os.WriteFile(scenesFile, []byte(strings.Join(segments, "\n")), 0644)
	if err != nil {
		return fmt.Errorf("ошибка записи файла сцен: %v", err)
	}

	// Собираем видео из сегментов
	trimCmd := exec.CommandContext(ctx, ffmpegPath,
		"-f", "concat",
		"-safe", "0",
		"-i", scenesFile,
		"-c", "copy",
		"-y", outputPath)

	return trimCmd.Run()
}

// Парсинг вывода ffmpeg для определения смены сцен
func parseSceneChanges(output string) []float64 {
	var scenes []float64
	lines := strings.Split(output, "\n")

	// Добавляем начало видео
	scenes = append(scenes, 0.0)

	for _, line := range lines {
		if strings.Contains(line, "pts_time:") {
			parts := strings.Split(line, "pts_time:")
			if len(parts) > 1 {
				timeStr := strings.Split(parts[1], " ")[0]
				time, err := strconv.ParseFloat(timeStr, 64)
				if err == nil {
					scenes = append(scenes, time)
				}
			}
		}
	}

	return scenes
}

// Проверка наличия речи в сегменте
func checkForSpeech(ctx context.Context, videoPath string, start, end float64) (bool, error) {
	// Извлекаем аудио из сегмента
	cmd := exec.CommandContext(ctx, ffmpegPath,
		"-i", videoPath,
		"-ss", fmt.Sprintf("%.3f", start),
		"-to", fmt.Sprintf("%.3f", end),
		"-filter:a", "volumedetect",
		"-f", "null", "-")

	output, err := cmd.CombinedOutput()
	if err != nil {
		return false, err
	}

	// Анализируем вывод для определения громкости
	outputStr := string(output)
	var meanVolume float64 = -100.0 // Значение по умолчанию

	for _, line := range strings.Split(outputStr, "\n") {
		if strings.Contains(line, "mean_volume:") {
			parts := strings.Split(line, "mean_volume:")
			if len(parts) > 1 {
				volStr := strings.TrimSpace(strings.Split(parts[1], "dB")[0])
				meanVolume, _ = strconv.ParseFloat(volStr, 64)
			}
		}
	}

	// Если средняя громкость выше порога, считаем что есть речь
	return meanVolume > -30.0, nil
}
*/