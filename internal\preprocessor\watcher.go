/*
Watcher observes the input directory for new files and sends them to the file queue.
*/
package preprocessor

import (
	"context"
	"log"
	"path/filepath"

	"github.com/fsnotify/fsnotify"
)

type FileExtension string

const (
	Video FileExtension = "mp4" // TODO: add mkv, avi, mov, etc.
	Audio FileExtension = "wav" // TODO: add acc, mp3, ogg, m4a, flac, etc.
)

type FilesWatcher struct {
	InputDir      string
	FileExtension FileExtension
	FileQueue     chan<- string
}

func NewFilesWatcher(inputDir string, fileExtension FileExtension, fileQueue chan<- string) *FilesWatcher {
	return &FilesWatcher{
		InputDir:      inputDir,
		FileExtension: fileExtension,
		FileQueue:     fileQueue,
	}
}

func (w *FilesWatcher) WatchInputDir(ctx context.Context) {

	// init watcher
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		log.Fatal(err)
	}
	defer watcher.Close()

	// add input dir to watcher
	if err := watcher.Add(w.InputDir); err != nil {
		log.Fatalf("Cannot add %s to watcher: %v", w.InputDir, err)
	}

	// search existing files on start
	files, err := filepath.Glob(filepath.Join(w.InputDir, "*."+string(w.FileExtension)))
	if err != nil {
		log.Fatalf("Cannot search for existing files: %v", err)
	}

	// TODO:
	// add existing files to queue
	for _, f := range files {
		w.FileQueue <- f
	}
	defer close(w.FileQueue)

	// watch for new files
	for {
		select {
		case <-ctx.Done():
			return
		case ev := <-watcher.Events:
			// что здесь происходит?
			// ev.Op&fsnotify.Create != 0 - проверяет, что событие - это создание файла
			// filepath.Ext(ev.Name) == "."+string(w.FileExtension) - проверяет, что расширение файла соответствует ожидаемому
			if ev.Op&fsnotify.Create != 0 && filepath.Ext(ev.Name) == "."+string(w.FileExtension) {
				log.Printf("New file found: %s", ev.Name)
				w.FileQueue <- ev.Name
			}
		case err := <-watcher.Errors:
			log.Printf("Watcher error: %v", err)
		default:
			log.Println("Waiting for new files...")
		}
	}
}
