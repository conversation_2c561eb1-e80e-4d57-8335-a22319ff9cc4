package scheduler

import "github.com/iqhater/video_auto_proccessing_pipeline/internal/config"

type ResourceManager struct {

	// limits
	MaxConcurrency int // in goroutines
	MaxMemoryUsage int // in MB
	MaxDiskUsage   int // in MB
	MaxCPUUsage    int // in %
	MaxTaskCount   int // in tasks
	MaxQueueSize   int // in tasks

	// monitoring
	CurrentConcurrency int
	CurrentMemoryUsage int
	CurrentDiskUsage   int
	CurrentCPUUsage    int
	CurrentTaskCount   int
	CurrentQueueSize   int
}

func NewResourceManager(config config.Parser) *ResourceManager {
	return &ResourceManager{
		MaxConcurrency: 50,
		MaxMemoryUsage: 4000,
		MaxDiskUsage:   1000,
		MaxCPUUsage:    80,
		MaxTaskCount:   50,
		MaxQueueSize:   100,
	}
}
