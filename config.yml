# Config file for video processing pipeline

# Path video input params
video:
  input: ./input/video1.mp4
  output: ./output/video1_processed.mp4

# Processing pipeline
processing:

  # Preprocessing tasks setup (priority range, highest 40-50)
  pre:
    tasks:
      - id: norm
        name: normalize_container
        priority: 40
        params:
          codec: h264
          bitrate_kbps: 2000

      - id: meta
        name: extract_metadata
        priority: 41
        depends_on: [norm]
        params:
          extract_audio: true
          extract_video: true

  # Chunk processing tasks setup (priority range, middle 20-30)
  chunk:
    chunk_size_mb: 10
    tasks:
      - id: split
        name: split_chunks
        priority: 30
        depends_on: [meta]
        params:
          size_mb: 10

      - id: noise
        name: noise_reduction
        priority: 20
        depends_on: [split]
        params:
          threshold_db: -30
        
      - id: fillers 
        name: remove_fillers
        priority: 22
        depends_on: [noise]
        params:
          fillers:
            - ээ
            - ну

      - id: trim
        name: trim_silence
        priority: 23
        depends_on: [fillers]
        params:
          silence_threshold_db: -40
          min_duration_s: 0.3

  # Postprocessing tasks setup (priority range, lowest 0-10)
  post:
    tasks:
      - id: merge
        name: merge_chunks
        priority: 0
        depends_on: [trim]
        params:
          output: merged.mp4

      - id: timecodes
        name: add_timestamps
        priority: 2
        depends_on: [merge]
        params:
          interval_s: 30

      - id: final
        name: final_transcode
        priority: 1
        depends_on: [timecodes]
        params:
          codec: h264
          bitrate_kbps: 2000

# Resource manager to adjust load on machine
resource:
  max_concurrency: 50 # in goroutines
  max_memory_usage: 4096 # in MB
  max_disk_usage: 1024 # in MB
  max_cpu_usage: 80 # in %
  max_task_count: 50 # in tasks
  max_queue_size: 100 # in tasks

# Retry policy for task execution
policy:
  retry:
    max_retries: 3
    backoff: 1s # ???
    