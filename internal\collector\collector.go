/*
Отвечает за сборку финальных результатов:

Collector (collector.go) — регистрирует группы задач, подписывается на EventBus, считает завершения и агрегирует артефакты.
*/
package collector

import (
	tsk "github.com/iqhater/video_auto_proccessing_pipeline/internal/task"
	"github.com/iqhater/video_auto_proccessing_pipeline/pkg/eventbus"
)

type Collector struct {
	eventBus *eventbus.EventBus
	tsk.Tasker
	// groups  map[string][]*task.Task
	// results map[string]any
}

func NewCollector(eventBus *eventbus.EventBus) *Collector {
	return &Collector{
		eventBus: eventBus,
		// groups:   make(map[string][]*task.Task),
		// results:  make(map[string]any),
	}
}

// TODO:
func (c *Collector) GetReadyTasks() []*tsk.Task {

	if c.checkReadyTasks(c.GetTasks()) {
		return c.GetTasks()
	}
	return nil
}

func (c *Collector) checkReadyTasks(tasks []*tsk.Task) bool {

	for _, task := range c.eventBus.Notify("TaskCompleted", tasks) {
		if task.Status != tsk.StatusCompleted {
			return false
		}
	}
	return true
}
