package config

import (
	"gopkg.in/yaml.v3"
)

var _ Parser = (*YAMLParser)(nil)

type TaskYAMLConfig struct {
	ID        string         `yaml:"id"`
	Name      string         `yaml:"name"`
	Priority  int            `yaml:"priority"`
	DependsOn []string       `yaml:"depends_on"`
	Params    map[string]any `yaml:"params"`
}

type TasksYAMLWrapper struct {
	Tasks []*TaskYAMLConfig `yaml:"tasks"`
}

type ChunkYAMLConfig struct {
	ChunkSizeMB int               `yaml:"chunk_size_mb"`
	Tasks       []*TaskYAMLConfig `yaml:"tasks"`
}

type ProcessingYAMLConfig struct {
	Pre   *TasksYAMLWrapper `yaml:"pre"`
	Chunk *ChunkYAMLConfig  `yaml:"chunk"`
	Post  *TasksYAMLWrapper `yaml:"post"`
}

type YAMLParser struct {
	Input                     string `yaml:"video_input"`
	Output                    string `yaml:"video_output"`
	ProcessingYAMLConfig      `yaml:"processing"`
	YAMLResourceManagerConfig `yaml:"resource"`
	YAMLRetryPolicyConfig     `yaml:"policy"`
}

type YAMLResourceManagerConfig struct {
	MaxConcurrency int `yaml:"max_concurrency"`
	MaxMemoryUsage int `yaml:"max_memory_usage"`
	MaxDiskUsage   int `yaml:"max_disk_usage"`
	MaxCPUUsage    int `yaml:"max_cpu_usage"`
	MaxTaskCount   int `yaml:"max_task_count"`
	MaxQueueSize   int `yaml:"max_queue_size"`
}

type YAMLRetryPolicyConfig struct {
	MaxRetries int `yaml:"max_retries"`
	Backoff    int `yaml:"backoff"` // ???
}

func (p *YAMLParser) Parse(config []byte) error {
	if err := yaml.Unmarshal(config, &p); err != nil {
		return err
	}

	// DEBUG INFO
	// fmt.Printf("Config: %#v\n", p.Chunk.Tasks[1].DependsOn)
	return nil
}
