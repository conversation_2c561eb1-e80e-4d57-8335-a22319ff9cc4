package main

import (
	"context"

	"github.com/iqhater/video_auto_proccessing_pipeline/internal/config"
	"github.com/iqhater/video_auto_proccessing_pipeline/internal/preprocessor"
	"github.com/iqhater/video_auto_proccessing_pipeline/internal/scheduler"
	"github.com/iqhater/video_auto_proccessing_pipeline/pkg/eventbus"
)

func main() {

	// 0. Load config
	/* jsonFile, err := preprocessor.LoadFile("config.json")
	if err != nil {
		panic(err)
	} */

	yamlFile, err := preprocessor.LoadFile("config.yml")
	if err != nil {
		panic(err)
	}

	// jsonConfig := &config.JSONParser{}
	yamlConfig := &config.YAMLParser{}

	// err = config.Init(jsonConfig, jsonFile)
	err = config.Init(yamlConfig, yamlFile)
	if err != nil {
		panic(err)
	}

	// fmt.Printf("Config: %#v\n", jsonConfig)
	// fmt.Printf("Config: %#v\n", yamlConfig)

	// 1. Run preprocessing
	/* f, err := preprocessor.LoadFile("input/Rodion Beat #001 Demo Ourto_compressed.mp4")
	// f, err := preprocessor.LoadFile("input/Afro House Vibe Demo.mp4")
	if err != nil {
		panic(err)
	} */

	// TODO: move to plugin call into executor
	/* splitter := &chunks_splitter.ChunksSplitter{
		Source:    f,
		ChunkSize: 15, // in seconds
	}

	ctx := context.Background()

	chunks, chunksErr := splitter.Process(ctx)
	if chunksErr != nil {
		panic(chunksErr)
	}

	fmt.Printf("Chunks: %v\n", string(chunks)) */
	// return

	// 2. Run scheduler
	eventBus := eventbus.NewEventBus()

	ctx := context.Background()
	scheduler := scheduler.NewScheduler(ctx, yamlConfig, eventBus)

	errSched := scheduler.Run(ctx)
	if errSched != nil {
		panic(errSched)
	}

	// 3. Collect and merge results

	// 4. Output and save results

	// 5. Graceful shutdown

	// debug testing code
	// g := graph.New(graph.IntHash, graph.Directed(), graph.Acyclic())

	// _ = g.AddVertex(1)
	// _ = g.AddVertex(2)
	// _ = g.AddVertex(3)
	// _ = g.AddVertex(4)

	// _ = g.AddEdge(1, 2)
	// _ = g.AddEdge(1, 3)
	// _ = g.AddEdge(2, 3)
	// _ = g.AddEdge(2, 4)
	// _ = g.AddEdge(3, 4)

	// pipeline := task.NewPipeline(&task.ProccessImpl{})
	// pipeline.AddTask("task1", nil)
	// pipeline.AddTask("task2", nil)
	// pipeline.AddTask("task3", nil)
	// pipeline.AddTask("task4", nil)
	// pipeline.AddTask("task5", nil)

	/* task2 := pipeline.FindTaskByName("task2")
	pipeline.DeleteTaskByID(task2.ID) */

	/* err := pipeline.Run(context.Background())
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} */

	// slices.Reverse(pipeline.GetTasks())

	// for _, task := range pipeline.GetTasks() {
	// 	fmt.Printf("Task: %s, ID: %s, Status: %s \n", task.Title, task.ID, task.Status)
	// }

}
