package eventbus

import (
	"slices"
	"sync"
)

type Handler func(data any)

type EventBus struct {
	subscribers map[string][]Handler
	mu          sync.RWMutex
}

func NewEventBus() *EventBus {
	return &EventBus{
		subscribers: make(map[string][]Handler),
	}
}

func (eb *EventBus) Subscribe(event string, h Handler) {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	eb.subscribers[event] = append(eb.subscribers[event], h)
}

func (eb *EventBus) Unsubscribe(event string, h Handler) {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	for i, fn := range eb.subscribers[event] {
		if &h == &fn {
			eb.subscribers[event] = slices.Delete(eb.subscribers[event], i, i+1)
			break
		}
	}
}

func (eb *EventBus) Publish(event string, data any) {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	for _, h := range eb.subscribers[event] {
		h(data)
	}
}
