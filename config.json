{"video_input": "./raw/video1.mp4", "video_output": "./processed/video1_processed.mp4", "processing": {"pre": {"tasks": [{"name": "normalize_container", "params": {}}, {"name": "extract_metadata", "params": {}}]}, "chunk": {"chunk_size_mb": 10, "tasks": [{"name": "noise_reduction", "params": {"threshold_db": -30}}, {"name": "remove_fillers", "params": {"fillers": ["ээ", "ну"]}}, {"name": "trim_silence", "params": {"silence_threshold_db": -40, "min_duration_s": 0.3}}]}, "post": {"tasks": [{"name": "merge_chunks", "params": {"output": "merged.mp4"}}, {"name": "add_timestamps", "params": {"interval_s": 30}}, {"name": "final_transcode", "params": {"codec": "h264", "bitrate_kbps": 2000}}]}}}