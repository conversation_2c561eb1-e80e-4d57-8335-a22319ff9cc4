## 🧠 Концептуальная схема: Архитектура пайплайна видеообработки

### 🧩 Основные компоненты

`Preprocessor`

* Назначение: подготовка видео перед обработкой.

* Примеры: нарезка на чанки, извлечение аудио, анализ метаданных.

* Фаза: до построения графа.

`TaskDefinition`

* **Что описывает**: тип задачи, имя, параметры и зависимости.

* **Используется в**: построении графа, определении очередности.

`TaskInstance`

* **Что делает**: конкретное выполнение задачи.

* **Содержит**: FSM (машину состояний) для отслеживания статуса (`pending`, `running`, `completed`, `failed`, `cancelled`).

`DependencyGraph`

* **Структура**: направленный ацикличный граф (DAG), где вершины — задачи, ребра — зависимости.

* **Цель**: не дать запустить задачу, пока не завершены зависимости.

* **Состояние задач**: обновляется после исполнения через `EventBus`.

`Scheduler`

* **Назначение**: оркестрация исполнения задач.

* **Обходит граф**: ищет `pending` задачи с удовлетворенными зависимостями.

* **Передаёт задачи в**: `PriorityQueue`.

`PriorityQueue`

* **Роль**: двоичная куча с приоритетами задач.

* **Приоритеты**: `pre: 40–50`, `chunk: 20–30`, `post: 0–10`.

`Executor`

* **Что делает**: берёт задачи из очереди и исполняет их через `Worker Pool`.

* **Следит**: за ресурсами, таймаутами, отменой.

`RetryPolicy`

* **Обрабатывает**: неудачи задач.

* **Поведение**: в зависимости от политики — перезапуск, игнор, отмена.

`EventBus`

* **Оповещает**: все заинтересованные компоненты (например, `Collector`, `Scheduler`).

* **Когда**: меняется статус задачи (`TaskInstance` завершена, упала и т.д.).

`Collector`

* **Следит**: за завершением группы задач (например, по чанкам).

* **Собирает**: результаты во временные файлы или единый артефакт.

* **Запускает**: задачи постобработки после завершения основной группы.

`Plugin Interface`

* **Для расширения**: пользователи могут добавлять свои задачи.

* **Требование**: реализовать интерфейс `TaskPlugin`.

### 🔁 Цикл исполнения

1. Загружается `config.yaml` (будет поддержка JSON/TOML).

2. `Preprocessor` разбивает видео, извлекает нужные данные.

3. Формируются `TaskDefinition` и `DependencyGraph`.

4. `Scheduler` планирует задачи, подает в `PriorityQueue`.

5. `Executor` запускает задачи.

6. `TaskInstance` через FSM отслеживает статус.

7. `EventBus` оповещает `Collector`, `Scheduler`.

8. `Collector` ждет завершения группы задач.

9. После этого — новая задача `TaskInstance` с `post-processing`.

10. Цикл повторяется до полного завершения.
