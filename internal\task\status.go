package task

/*
Pending → Running → (Completed | Failed → Retrying → Completed)
                                  ↑
                              Cancelled
*/

type TaskStatus string

const (
	StatusPending   TaskStatus = "PENDING"
	StatusRunning   TaskStatus = "RUNNING"
	StatusCompleted TaskStatus = "COMPLETED"
	StatusFailed    TaskStatus = "FAILED"
	StatusRetrying  TaskStatus = "RETRYING"
	StatusCancelled TaskStatus = "CANCELLED"
)
