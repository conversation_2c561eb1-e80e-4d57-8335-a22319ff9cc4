package scheduler

import (
	"container/heap"
	"context"
	"iter"
	"log"

	"github.com/dominikbraun/graph"
	"github.com/google/uuid"
	"github.com/iqhater/video_auto_proccessing_pipeline/internal/config"
	tsk "github.com/iqhater/video_auto_proccessing_pipeline/internal/task"
)

var _ tsk.Tasker = (*DependencyGraph)(nil)

type DependencyGraph struct {
	TasksGraph graph.Graph[string, string]
	executor   Executor
	heap       *PriorityQueue
}

func InitDependencyGraph(tasks []*config.TaskYAMLConfig) *DependencyGraph {

	g := graph.New(graph.StringHash, graph.Directed(), graph.Acyclic())

	for _, task := range tasks {

		// add vertex
		err := g.AddVertex(task.ID)
		if err != nil {
			log.Fatal(err)
			return nil
		}

		// add dependency edge
		if len(task.DependsOn) > 0 {
			for _, dependency := range task.DependsOn {
				err = g.Add<PERSON>dge(task.ID, dependency)
				if err != nil {
					log.Printf("Error adding edge: %s -> %s: %v", task.ID, dependency, err)
				}
			}
		}
	}

	// Инициализируем пустую очередь с приоритетами
	pq := make(PriorityQueue, 0)
	heap.Init(&pq)

	executor := NewExecutor(nil)

	return &DependencyGraph{
		TasksGraph: g,
		executor:   executor,
		heap:       &pq,
	}
}

func NewTask(title string, opts map[string]any) *tsk.Task {
	return &tsk.Task{
		ID:       uuid.New(),
		Title:    title,
		Priority: -1,
		Opts:     opts,
		Status:   tsk.StatusPending,
		Index:    -1,
	}
}

func IterTasksGraph(g graph.Graph[string, string]) iter.Seq[string] {
	return func(yield func(string) bool) {
		visited := map[string]bool{}
		edges, _ := g.Edges()

		for _, edge := range edges {
			vertex, err := g.Vertex(edge.Source)
			if err != nil {
				continue // Пропускаем вершину при ошибке вместо паники
			}

			// Модифицируем функцию обратного вызова для DFS
			shouldContinue := true
			graph.DFS(g, vertex, func(v string) bool {
				if visited[v] {
					return true // Пропускаем уже посещенные вершины
				}
				visited[v] = true

				// Если yield вернул false, прекращаем обход
				shouldContinue = yield(v)
				return shouldContinue
			})

			// Если yield вернул false, прекращаем обход всего графа
			if !shouldContinue {
				break
			}
		}
	}
}

func (dp *DependencyGraph) Run(ctx context.Context) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
		for taskName := range IterTasksGraph(dp.TasksGraph) {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				// Получаем задачу по имени из хранилища задач
				task := dp.FindTaskByName(taskName)
				if task == nil {
					continue
				}

				// отбираем задачи только со статусом "PENDING"
				if task.Status == tsk.StatusPending {
					// add to heap
					dp.heap.AddToHeap(task)

					// Проверяем наличие executor перед вызовом
					if dp.executor != nil {
						if err := dp.executor.Run(ctx, task); err != nil {
							log.Printf("Error processing task %s: %v", task.Title, err)
							// Продолжаем выполнение других задач вместо возврата ошибки
							task.Status = tsk.StatusFailed
							continue
						}
					} else {
						log.Printf("Executor не инициализирован для задачи %s", task.Title)
						continue
					}
				}
			}
		}
	}
	return nil
}

func (dp *DependencyGraph) Stop(ctx context.Context) error {
	// TODO: implement
	return nil
}

func (dp *DependencyGraph) AddTask(title string, opts map[string]any) {

	newTask := NewTask(title, opts)
	dp.TasksGraph.AddVertex(newTask.Title)
	dp.TasksGraph.AddEdge(newTask.Title, newTask.Opts["depends_on"].(string))
}

func (dp *DependencyGraph) FindTaskByName(taskName string) *tsk.Task {
	// Проверяем существование вершины в графе
	_, _, err := dp.TasksGraph.VertexWithProperties(taskName)
	if err != nil {
		return nil
	}

	// Создаем новый Task с найденным именем
	// Поскольку граф хранит только имена, восстанавливаем минимальную структуру
	task := &tsk.Task{
		ID:     uuid.New(), // Тут мы теряем оригинальный ID, нужно добавить хранение метаданных
		Title:  taskName,
		Status: tsk.StatusPending,
		Opts:   make(map[string]any),
	}
	return task
}

func (dp *DependencyGraph) FindTaskByID(taskID uuid.UUID) *tsk.Task {

	for taskName := range IterTasksGraph(dp.TasksGraph) {

		task := dp.FindTaskByName(taskName)
		if task != nil && task.ID == taskID {
			return task
		}
	}
	return nil
}

func (dp *DependencyGraph) GetTasks() []*tsk.Task {

	var tasks []*tsk.Task

	// Здесь нужно реализовать получение всех задач из графа
	// Пример:
	for taskName := range IterTasksGraph(dp.TasksGraph) {

		task := dp.FindTaskByName(taskName)
		if task != nil {
			tasks = append(tasks, task)
		}
	}
	return tasks
}

func (dp *DependencyGraph) DeleteTaskByID(taskID uuid.UUID) {

	for taskName := range IterTasksGraph(dp.TasksGraph) {

		task := dp.FindTaskByName(taskName)
		if task != nil && task.ID == taskID {
			dp.TasksGraph.RemoveVertex(taskName)
			return
		}
	}
}
