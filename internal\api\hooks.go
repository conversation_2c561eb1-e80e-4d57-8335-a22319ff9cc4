package api

import (
	"context"

	tsk "github.com/iqhater/video_auto_proccessing_pipeline/internal/task"
)

type Hook interface {
	Before(ctx context.Context, task *tsk.Task) error
	After(ctx context.Context, task *tsk.Task) error
}

type PreprocessingHook interface {
	Hook
}

type ChunkHook interface {
	Hook
}

type PostprocessingHook interface {
	Hook
}

type PipelineHook struct {
	PreprocessingHook
	ChunkHook
	PostprocessingHook
}
