/*
Отвечает за планирование и исполнение:

Scheduler (scheduler.go) — основной цикл: поиск Pending, передача в Executor, обработка RetryPolicy.

Executor + WorkerPool (executor.go) — запускает задачи в горутинах под контролем ResourceManager.

RetryPolicy (retrypolicy.go) — логика повторных попыток.

ResourceManager (resourcemgr.go) — семафор для ограничения параллелизма.

EventBus (eventbus.go) — шина событий для уведомлений о смене статусов.
*/
package scheduler

import (
	"context"
	"log"

	"github.com/iqhater/video_auto_proccessing_pipeline/internal/config"
	"github.com/iqhater/video_auto_proccessing_pipeline/internal/task"
	"github.com/iqhater/video_auto_proccessing_pipeline/pkg/eventbus"
)

type Scheduler struct {
	eventBus *eventbus.EventBus
	task.Tasker
}

func NewScheduler(ctx context.Context, yamlConfig *config.YAMLParser, eventBus *eventbus.EventBus) *Scheduler {

	// 1. init dependency graph
	tasks := []*config.TaskYAMLConfig{}

	tasks = append(tasks, yamlConfig.Pre.Tasks...)
	tasks = append(tasks, yamlConfig.Chunk.Tasks...)
	tasks = append(tasks, yamlConfig.Post.Tasks...)

	depsGraph := InitDependencyGraph(tasks)

	graphErr := depsGraph.Run(ctx)
	if graphErr != nil {
		log.Fatalln(graphErr)
	}

	// 2. init heap
	// 3. Load into priority queue (binary heap)
	// Some items and their priorities.
	heapTasks := []*TaskHeap{
		NewTaskHeap("task1", 3, nil),
		NewTaskHeap("task2", 2, nil),
		NewTaskHeap("task3", 1, nil),
		NewTaskHeap("task4", 5, nil),
		NewTaskHeap("task5", 4, nil),
	}

	err := InitHeap(heapTasks)
	if err != nil {
		panic(err)
	}

	return &Scheduler{
		eventBus: eventBus,
	}
}

func (s *Scheduler) Run(ctx context.Context) error {
	// TODO: implement
	// heap.Push(&pq, task)
	return nil
}
